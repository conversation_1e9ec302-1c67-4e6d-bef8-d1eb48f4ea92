cmake_minimum_required(VERSION 3.19)
project(TPCC LANGUAGES C CXX)

set(CMAKE_NINJA_FORCE_RESPONSE_FILE ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(SOURCE_FILES "")
set(UI_FILES "")

# add_include
function(add_include dir)
    file(GLOB SUBDIRS RELATIVE ${dir} ${dir}/*)
    foreach(subdir ${SUBDIRS})
        if(IS_DIRECTORY ${dir}/${subdir})
            include_directories(${dir}/${subdir})
            add_include(${dir}/${subdir})
        endif()
    endforeach()
endfunction()
# get_files_from_dir_recursive
function(get_files_from_dir_recursive dir patterns files)
    string(REPLACE " " ";" pattern_list ${patterns})
    foreach(pattern ${pattern_list})
        file(GLOB files_in_dir "${dir}/*${pattern}")
        list(APPEND ${files} ${files_in_dir})
    endforeach()
    file(GLOB SUBDIRS RELATIVE ${dir} ${dir}/*)
    foreach(subdir ${SUBDIRS})
        if(IS_DIRECTORY ${dir}/${subdir})
            get_files_from_dir_recursive(${dir}/${subdir} ${patterns} ${files})
        endif()
    endforeach()
    foreach(file IN LISTS ${files})
        string(REPLACE ${PROJECT_SOURCE_DIR}/ "" relative_file_path ${file})
        list(APPEND relative_path_files ${relative_file_path})
        get_filename_component(file_extension ${file} EXT)
        if(file_extension STREQUAL ".ui")
            list(APPEND local_ui_files ${relative_file_path})
        endif()
    endforeach()
    # set(${files} ${${files}} PARENT_SCOPE)
    set(${files} ${relative_path_files} PARENT_SCOPE)
    set(UI_FILES ${local_ui_files} PARENT_SCOPE)
endfunction()
# cmake_log
function(cmake_log msg)
    message(STATUS "\tCMAKE_LOG || ${msg}")
endfunction()

add_include(${PROJECT_SOURCE_DIR})
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/../Updater ".json" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/DeviceConnector ".cpp .h .ui" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/DeviceMainWindow ".cpp .h .ui" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/DeviceField ".cpp .h .ui" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/DeviceWidget ".cpp .h .ui" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/CustomWidget ".cpp .h .ui" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/ThirdPartyResource ".qrc .rc" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/ThirdPartyResource/Component/TKSpline ".h" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/CustomFunction ".cpp .h" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBHID/Device ".cpp .h" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBHID/API ".cpp .h" SOURCE_FILES)
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBHID/SDK ".h" SOURCE_FILES)
if(WIN32)
    get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/../Packager/Win ".bat" SOURCE_FILES)
    get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBHID/SDK/win ".c" SOURCE_FILES)
    get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBAudio/SDK/WIN ".cpp .h" SOURCE_FILES)
elseif(APPLE)
    get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/../Packager/Mac/ ".sh" SOURCE_FILES)
    get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBHID/SDK/mac ".c" SOURCE_FILES)
    get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBAudio/SDK/MAC ".cpp .h" SOURCE_FILES)
    file(GLOB MAC_DYLIB_FILES "${PROJECT_SOURCE_DIR}/USBAudio/SDK/MAC/*.dylib")
    list(APPEND SOURCE_FILES ${MAC_DYLIB_FILES})
endif()
get_files_from_dir_recursive(${PROJECT_SOURCE_DIR}/USBAudio/API ".cpp .h" SOURCE_FILES)

# foreach(files ${SOURCE_FILES})
#     cmake_log("${files}")
# endforeach()

find_package(Qt6 6.5 REQUIRED COMPONENTS Core Widgets Network Charts Svg)

qt_standard_project_setup()

qt_add_executable(${PROJECT_NAME}
    WIN32 MACOSX_BUNDLE
    main.cpp
    ${SOURCE_FILES}
)

qt_add_ui(${PROJECT_NAME} SOURCES ${UI_FILES})

set(APP_NAME "M Control Center")
set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME ${APP_NAME})
if(WIN32)
    set(APP_VERSION "1.1.15")
elseif(APPLE)
    set(CMAKE_OSX_DEPLOYMENT_TARGET "10.15" CACHE STRING "Minimum OS X deployment version")
    set(CMAKE_OSX_ARCHITECTURES "x86_64;arm64" CACHE STRING "Build architectures for macOS" FORCE)
    
    set(APP_VERSION "1.1.5")
    set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_BUNDLE_BUNDLE_NAME ${APP_NAME}
        MACOSX_BUNDLE_BUNDLE_VERSION ${APP_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${APP_VERSION}
        MACOSX_BUNDLE_GUI_IDENTIFIER ${APP_NAME}
        MACOSX_BUNDLE_EXECUTABLE_NAME ${APP_NAME}
        MACOSX_BUNDLE_ICON_FILE "SeriesM.icns"
    )

    set(icns ${PROJECT_SOURCE_DIR}/ThirdPartyResource/Icon/SeriesM.icns)
    set(license_file ${PROJECT_SOURCE_DIR}/USBAudio/SDK/MAC/libtlusbdfuapi.dylib.license.ini)

    set(COPY_COMMANDS "")

    list(APPEND COPY_COMMANDS
        COMMAND ${CMAKE_COMMAND} -E make_directory $<TARGET_BUNDLE_DIR:${PROJECT_NAME}>/Contents/Resources
        COMMAND ${CMAKE_COMMAND} -E make_directory $<TARGET_BUNDLE_DIR:${PROJECT_NAME}>/Contents/Frameworks)

    list(APPEND COPY_COMMANDS
        COMMAND ${CMAKE_COMMAND} -E copy_if_different ${icns} $<TARGET_BUNDLE_DIR:${PROJECT_NAME}>/Contents/Resources/)

    if(EXISTS ${license_file})
        list(APPEND COPY_COMMANDS
            COMMAND ${CMAKE_COMMAND} -E copy_if_different ${license_file} $<TARGET_BUNDLE_DIR:${PROJECT_NAME}>/Contents/Resources/)
    endif()

    if(MAC_DYLIB_FILES)
        foreach(dylib_file ${MAC_DYLIB_FILES})
            list(APPEND COPY_COMMANDS
                COMMAND ${CMAKE_COMMAND} -E copy_if_different ${dylib_file} $<TARGET_BUNDLE_DIR:${PROJECT_NAME}>/Contents/Frameworks/)
        endforeach()
    endif()

    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        ${COPY_COMMANDS}
        COMMENT "Copying files to app bundle"
    )

    find_library(COREAUDIO_FRAMEWORK CoreAudio)
    if(NOT COREAUDIO_FRAMEWORK)
        message(FATAL_ERROR "CoreAudio framework not found")
    endif()
    target_link_libraries(${PROJECT_NAME} PRIVATE ${COREAUDIO_FRAMEWORK})
endif()

target_link_libraries(${PROJECT_NAME}
    PRIVATE
        Qt6::Core
        Qt6::Widgets
        Qt6::Network
        Qt6::Charts
        Qt6::Svg
)

target_compile_definitions(${PROJECT_NAME} PRIVATE APP_VERSION="${APP_VERSION}")

include(GNUInstallDirs)

install(TARGETS ${PROJECT_NAME}
    BUNDLE  DESTINATION .
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

qt_generate_deploy_app_script(
    TARGET ${PROJECT_NAME}
    OUTPUT_SCRIPT deploy_script
    NO_UNSUPPORTED_PLATFORM_ERROR
)

install(SCRIPT ${deploy_script})
