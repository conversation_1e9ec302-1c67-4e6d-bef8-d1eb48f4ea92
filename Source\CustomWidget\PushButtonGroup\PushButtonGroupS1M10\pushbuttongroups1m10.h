#ifndef PUSHBUTTONGROUPS1M10__H
#define PUSHBUTTONGROUPS1M10__H


#include <QHash>
#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class PushButtonGroupS1M10;
}


class PushButtonGroupS1M10 : public QWidget
{
    Q_OBJECT
public:
    explicit PushButtonGroupS1M10(QWidget* parent=nullptr);
    ~PushButtonGroupS1M10();
    PushButtonGroupS1M10& setFont(QFont font);
    PushButtonGroupS1M10& setLanguage(QString language);
    PushButtonGroupS1M10& setState(QString button, QString state, bool needEmit=true);
    QString getState(QString button);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void showEvent(QShowEvent*) override { resizeEvent(nullptr); }
private:
    Ui::PushButtonGroupS1M10* ui;
    QFont mFont;
    unsigned int mBitmap=0;
    QHash<unsigned int, QString> mStyle;
private slots:
    void on_PushButtonEQ_clicked(bool checked);
signals:
    void stateChanged(QString button, QString state);
};


#endif // PUSHBUTTONGROUPS1M10__H

