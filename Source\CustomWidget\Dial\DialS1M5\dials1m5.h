#ifndef DialS1M5_H
#define DialS1M5_H

#include <QFont>
#include <QRect>
#include <QColor>
#include <QWidget>
#include <QPointF>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QResizeEvent>

class DialS1M5 : public QWidget
{
    Q_OBJECT
public:
    enum Mode{
        normal,
        fixedRnage
    };
    explicit DialS1M5(QWidget* parent=nullptr);
    ~DialS1M5();
    DialS1M5& setFont(QFont font);
    DialS1M5& setPlaceText(const QString& text);
    DialS1M5& setMode(Mode mode);
    DialS1M5& setRangeFixed(const QList<float>& ranges);
    DialS1M5& setValue(float value);
    DialS1M5& setDefault(float value);
    DialS1M5& setRange(float min, float max);
    DialS1M5& setSensitivity(int sensitivity);
    DialS1M5& setStep(float step);
    DialS1M5& setStepWheel(float step);
    DialS1M5& setStepKey(float step);
    DialS1M5& setPrecision(int precision);
    DialS1M5& setMovable(bool status=true);
    DialS1M5& setDoublePercent(bool status=true);
    DialS1M5& setColorBG(QColor color);
    DialS1M5& setColorDial(QColor color);
    DialS1M5& setColorCircleBG(QColor color);
    DialS1M5& setColorCircleValue(QColor color);
    DialS1M5& setColorHandle(QColor color);
    DialS1M5& setColorText(QColor color);
    float getValue() { return mValue; }
    float getDefault() { return mValueDefault; }
    DialS1M5& showArrow(bool status=true);
    DialS1M5& showCircle(bool status=true);
    DialS1M5& showText(bool status=true);
    DialS1M5& showSign(bool status=true);
    DialS1M5& showInfinity(bool state=true);
    DialS1M5& showInfinitesimal(bool state=true);
protected:
    void resizeEvent(QResizeEvent* e) override;
    void paintEvent(QPaintEvent* e) override;
    void mouseDoubleClickEvent(QMouseEvent* e) override;
    void mousePressEvent(QMouseEvent* e) override;
    void mouseMoveEvent(QMouseEvent* e) override;
    void mouseReleaseEvent(QMouseEvent* e) override;
    void wheelEvent(QWheelEvent* e) override;
    void keyPressEvent(QKeyEvent* e) override;
    QPixmap extractArcPartSquare(const QPixmap &source, qreal mCurValueAngle, bool mDoublePercent);

private:
    Mode mMode=normal;
    QList<float> mRanges;
    int mIndex=0;
    bool mMouseEnabled=true;
    bool mPressed=false;
    float mPressedValue=0;
    int mPressedIndex=0;
    QPointF mPressedPoint;
    bool mDoublePercent=false;
    bool mValueShowArrow=true;
    bool mValueShowCircle=true;
    bool mValueShowText=true;
    bool mValueShowSign=false;
    bool mShowInfinity=false;
    bool mShowInfinitesimal=false;
    float mValue=25;
    float mValueDefault=25;
    float mValueMin=0;
    float mValueMax=50;
    float mValueStep=1;
    float mValueStepKey=1;
    float mValueStepWheel=1;
    int mPrecision=0;
    int mSensitivity=5;
    QRect mRectDial;
    QFont mFont;
    QColor mColorBG=QColor(22, 22, 22);
    QColor mColorDial=QColor(53, 53, 53);
    QColor mColorCircleBG=QColor(46, 46, 46);
    QColor mColorCircleValue=QColor(67, 207, 124);
    QColor mColorHandle=QColor(67, 207, 124);
    QColor mColorText=QColor(67, 207, 124);
    QString mPlaceText="+88";
    void drawBG(QPainter* painter);
    void drawElement(QPainter* painter);
signals:
    void valueChanged(float value);
};

#endif // DialS1M5_H
