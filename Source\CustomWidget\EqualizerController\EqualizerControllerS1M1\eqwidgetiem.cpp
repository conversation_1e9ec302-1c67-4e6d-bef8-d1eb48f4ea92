#include "eqwidgetiem.h"
#include <qcombobox.h>
#include <QApplication>
#include <QHash>
#include <functional>
#include <QVariant>
#include <QLabel>
#include <QCheckBox>
#include <QFont>
#include <QResizeEvent>
#include "comboboxs1m3.h"
#include "dials1m5.h"
#include "globalfont.h"

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : Q<PERSON>rame(parent)
    , mData(index)
    , mIndex(index)
    , mSizeFactor(1.0)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #A1A1A1;"
        "}"
        "QCheckBox::indicator {"
        "    width: 20px;"
        "    height: 20px;"
        "    border-radius: 3px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}"
    );
    mHashLanguages.insert(mTypeComboBox, {{"English", {"peek", "lowpass", "highpass", "lowshelf", "highshelf"}}, {"Chinese", {"峰值", "低通", "高通", "低架", "高架"}}});
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setAlignment(Qt::AlignCenter);

    mTypeComboBox = new ComboBoxS1M3(this, Qt::AlignCenter);
    mTypeComboBox->setColor({161,161,161});
    mTypeComboBox->setIndicatorWHRatio(0.5);
    QVector<QString> types = {
        "peek",
        "lowpass",
        "highpass",
        "lowshelf",
        "highshelf"
    };
    mTypeComboBox->addItems(types);

    mGainDial = new DialS1M5(this);
    mGainDial->setRange(-12.0f, 12.0f)
              .setDefault(0.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setStepWheel(0.5f)
              .setStepKey(1.0f)
              .setPrecision(1)
              .showSign(true)
              .setPlaceText("00.0000");

    QVector<float> frequencyRanges = {20,21,23,25,26,28,30,32,35,37,40,43,46,49,53,56,60,65,69,74,80,85,91,98,105,112,121,129,138,148,159,170,182,195,209,224,240,258,276,296,317,340,364,390,418,448,480,514,551,590,632,678,726,778,834,893,957,1026,1099,1178,1262,1352,1449,1552,1664,1783,1910,2047,2193,2350,2518,2698,2891,3098,3319,3557,3811,4083,4376,4688,5024,5383,5768,6181,6623,7096,7604,8148,8730,9355,10024,10741,11509,12332,13214,14159,15172,16257,17419,18665,20000,22000};
    QVector<float> qValueRanges = {0.1000,0.1040,0.1120,0.1190,0.1250,0.1350,0.1405,0.1563,0.1953,0.2000,0.2168,0.2293,0.2441,0.2679,0.3052,0.3433,0.3815,0.4239,0.4421,0.4768,0.5395,0.5960,0.6800,0.7070,0.9030,1.1642,1.3206,1.4552,1.8190,2.0213,2.2737,2.4120,2.5623,2.8422,3.1452,3.5527,3.8477,4.4409,4.9865,5.5511,6.0724,6.9389,7.7348,8.6736,10.8420,13.5525,15.0000};
    mFrequencyDial = new DialS1M5(this);
    mFrequencyDial->setMode(DialS1M5::fixedRnage)
                .setRangeFixed(frequencyRanges)
                .setDefault(632.0f)
                .setValue(632.0f)
                .setStep(1.0f)
                .setStepWheel(1.0f)
                .setStepKey(1.0f)
                .setPrecision(0)
              .setPlaceText("00.0000");

    mQValueDial = new DialS1M5(this);
    mQValueDial->setMode(DialS1M5::fixedRnage)
                .setRangeFixed(qValueRanges)
                .setDefault(0.707f)
                .setValue(0.707f)
                .setStep(1.0f)
                .setStepWheel(1.0f)
                .setStepKey(1.0f)
                .setPrecision(4)
              .setPlaceText("00.0000");

    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setChecked(true);
}

void EqWidgetIem::setupConnections()
{
    QHash<EqWidgetItemData::ChangeFlag, std::function<void(const QVariant&)>> changeHandlers = {
        { EqWidgetItemData::TYPE_CHANGED, [this](const QVariant& arg) {
             mData.updateType(arg.toString());
        }},
        { EqWidgetItemData::GAIN_CHANGED, [this](const QVariant& arg) {
            mData.updateGain(arg.toFloat());
        }},
        { EqWidgetItemData::FREQUENCY_CHANGED, [this](const QVariant& arg) {
            mData.updateFrequency(arg.toFloat());
        }},
        { EqWidgetItemData::QVALUE_CHANGED, [this](const QVariant& arg) {
            mData.updateQValue(arg.toFloat());
        }},
        { EqWidgetItemData::ENABLED_CHANGED, [this](const QVariant& arg) {
            mData.updateEnabled(arg.toBool());
        }}
    };
    auto handleAndEmit =[=](EqWidgetItemData::ChangeFlag flag, const QVariant& arg){
        changeHandlers.value(flag)(arg);
        emit dataChanged(mIndex, mData);
        mData.clearAllChanged();
    };

    connect(mTypeComboBox, &ComboBoxS1M3::activated, this, [handleAndEmit](int index) {
        handleAndEmit(EqWidgetItemData::TYPE_CHANGED, QVariant(index));
    });
    connect(mGainDial, &DialS1M5::valueChanged, this, [handleAndEmit](float value) {
        handleAndEmit(EqWidgetItemData::GAIN_CHANGED, QVariant(value));
    });
    connect(mFrequencyDial, &DialS1M5::valueChanged, this, [handleAndEmit](float value) {
        handleAndEmit(EqWidgetItemData::FREQUENCY_CHANGED, QVariant(value));
    });
    connect(mQValueDial, &DialS1M5::valueChanged, this, [handleAndEmit](float value) {
        handleAndEmit(EqWidgetItemData::QVALUE_CHANGED, QVariant(value));
    });
    connect(mEnabledCheckBox, &QCheckBox::clicked, this, [handleAndEmit](bool enabled) {
        handleAndEmit(EqWidgetItemData::ENABLED_CHANGED, QVariant(enabled));
    });
}

EqWidgetIem& EqWidgetIem::setFont(QFont font)
{
    mFont = font;
    mTypeComboBox->setFont(mFont);
    mGainDial->setFont(mFont);
    mFrequencyDial->setFont(mFont);
    mQValueDial->setFont(mFont);
    resizeEvent(nullptr);
    return *this;
}

void EqWidgetIem::setSizeFactor(double sizeFactor)
{
    mSizeFactor = sizeFactor;
    adjustFontAndSize();
}

void EqWidgetIem::setData(const QString& type, const QString& data)
{
    if(type == "Type"){
        mTypeComboBox->setCurrentIndex(data.toInt());
        mData.updateType(data, false);
    }else if(type == "Gain"){
        mGainDial->setValue(data.toFloat());
        mData.updateGain(data.toFloat(), false);
    }else if(type == "Freq"){
        mFrequencyDial->setValue(data.toFloat());
        mData.updateFrequency(data.toFloat(), false);
    }else if(type == "Q"){
        mQValueDial->setValue(data.toFloat());
        mData.updateQValue(data.toFloat(), false);
    }else if(type == "Switch"){
        mEnabledCheckBox->setChecked(data.toInt());
        mData.updateEnabled(data.toInt(), false);
    }
}

void EqWidgetIem::setData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentIndex(data.type.toInt());
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getData() const
{
    return mData;
}

void EqWidgetIem::setLanguage(QString language)
{
    int i =0;
    for(auto element : mHashLanguages.keys()){
        for(auto& text : mHashLanguages.value(element).value(language)){
            if(auto it = qobject_cast<QComboBox*>(element))
                it->setItemText(i, text);
            i++;
        }
    }
}

void EqWidgetIem::setIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidgetIem::adjustFontAndSize()
{
    int w = width();
    int h = height();

    int currentY = 0;

    int labelHeight = 0.07*h;
    mItemLabel->setGeometry(0, currentY, w, labelHeight);
    currentY += labelHeight + 0.04878 * h;

    int comboHeight = 0.07 * h;
    int comboWidth = w * 0.9;
    mTypeComboBox->setGeometry((w - comboWidth) / 2, currentY, comboWidth, comboHeight);
    currentY += comboHeight + 0.09059*h;

    int dialHeight = 0.19 * h;
    mGainDial->setGeometry(0, currentY, w, dialHeight);
    currentY += dialHeight + 0.03484 * h;

    mFrequencyDial->setGeometry(0, currentY, w, dialHeight);
    currentY += dialHeight + 0.03484 * h;

    mQValueDial->setGeometry(0, currentY, w, dialHeight);
    currentY += dialHeight + 0.02439 * h;

    int checkHeight = 0.06 * h;
    mEnabledCheckBox->setGeometry((w - checkHeight) / 2, currentY, checkHeight, checkHeight);

    int checkBoxSize = mEnabledCheckBox->height() * 0.8;
    mEnabledCheckBox->setStyleSheet(QString(
    "QCheckBox::indicator {"
        "width: %1px;"
        "height: %1px;"
        "border-radius: %2px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}").arg(checkBoxSize).arg(checkBoxSize * 0.1)
    );

    QFont labelFont = mFont;
    labelFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mItemLabel->height()));
    mItemLabel->setFont(labelFont);

    QFont controlFont = mFont;
    controlFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, mTypeComboBox->height()*0.9));
    mTypeComboBox->setFont(controlFont);
    mGainDial->setFont(controlFont);
    mFrequencyDial->setFont(controlFont);
    mQValueDial->setFont(controlFont);
}
