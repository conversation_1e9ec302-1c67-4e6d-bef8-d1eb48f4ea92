chcp 65001
setlocal enabledelayedexpansion
@echo off
cls

set "Deployer=D:\QT\6.9.0\msvc2022_64\bin\windeployqt.exe"
set "Packager=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
set "Compressor=D:\7ZIP\7-Zip\7z.exe"
set "Application=M Control Center.exe"
set "Source=.\..\..\Build\Release"
set "Target=.\..\..\Build"

:InputSeries
set /p Series="Series: "
if not exist ".\Series!Series!\Extras" (
    echo "需要配置Series!Series!的打包环境"
    goto InputSeries
)
cls
set /p Version="Version: "
set "Source=!Source!\!Application!"
if not exist "!Target!\!Version!\!Version!" (
    mkdir "!Target!\!Version!\!Version!"
    mkdir "!Target!\!Version!\!Version!Test"
    copy "!Source!" "!Target!\!Version!\!Version!"
    "!Deployer!" "!Target!\!Version!\!Version!\!Application!"
    robocopy ".\Extras" "!Target!\!Version!\!Version!"
    robocopy "!Target!\!Version!\!Version!" "!Target!\!Version!\!Version!Test" /MIR
    "!Compressor!" a -tzip "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!.zip" "!Target!\!Version!\!Version!\*"
    explorer "!Target!\!Version!"
    start "" "!Target!\!Version!\!Version!Test\!Application!"
    "!Packager!" /DMyAppVersion=!Version! /DMyAppIsBeta=1 ".\Series!Series!\Script.iss"
) else (
    echo "版本: !Version!已存在"
)
echo.
echo.
echo 操作完成
echo.
endlocal
