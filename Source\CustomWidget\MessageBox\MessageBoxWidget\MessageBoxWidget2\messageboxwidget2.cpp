#include "globalfont.h"
#include "messageboxwidget2.h"
#include "ui_messageboxwidget2.h"


MessageBoxWidget2::MessageBoxWidget2(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::MessageBoxWidget2)
{
    ui->setupUi(this);
    QString style;
    style = "QLabel {"
            "   color: rgb(255, 255, 255);"
            "}";
    ui->Label1->setStyleSheet(style);
    ui->Label2->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(48, 48, 48);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton1->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(1, 150, 65);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton2->setStyleSheet(style);
    setLanguage("English");
}
MessageBoxWidget2::~MessageBoxWidget2()
{
    delete ui;
}


// override
void MessageBoxWidget2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height() / 12));
    QFont font(mFont);
    font.setPointSize(font.pointSize() - 4);
    ui->Label1->setFont(mFont);
    ui->Label2->setFont(mFont);
    ui->PushButton1->setFont(mFont);
    ui->PushButton2->setFont(mFont);
}


// slot
void MessageBoxWidget2::on_PushButton1_clicked()
{
    emit attributeChanged("", "Override", "1");
}
void MessageBoxWidget2::on_PushButton2_clicked()
{
    emit attributeChanged("", "Continue", "1");
}


// setter & getter
MessageBoxWidget2& MessageBoxWidget2::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MessageBoxWidget2& MessageBoxWidget2::setLanguage(QString language)
{
    if(language == "English")
    {
        ui->Label1->setText("The workspace already exists");
        ui->PushButton1->setText("Override");
        ui->PushButton2->setText("Continue");
    }
    else if(language == "Chinese")
    {
        ui->Label1->setText("工作区已存在");
        ui->PushButton1->setText("覆盖");
        ui->PushButton2->setText("继续");
    }
    return *this;
}
MessageBoxWidget2& MessageBoxWidget2::showItemText(QString text)
{
    ui->Label2->setText(text);
    return *this;
}

