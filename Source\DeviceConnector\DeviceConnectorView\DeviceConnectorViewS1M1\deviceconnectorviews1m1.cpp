#include "globalfont.h"
#include "deviceconnectorviews1m1.h"
#include "ui_deviceconnectorviews1m1.h"


DeviceConnectorViewS1M1::DeviceConnectorViewS1M1(QWidget *parent)
    : DeviceConnectorViewBase(parent)
    , ui(new Ui::DeviceConnectorViewS1M1)
{
    ui->setupUi(this);
    QString style;
    style = "QLabel {"
            "   color: rgb(255, 255, 255);"
            "}";
    ui->Page0Label1->setStyleSheet(style);
    ui->Page1Label1->setStyleSheet(style);
    ui->Page2Label1->setStyleSheet(style);
    ui->Page3Label1->setStyleSheet(style);
    ui->Page4Label1->setStyleSheet(style);
    ui->Page5Label1->setStyleSheet(style);
    ui->Page6Label1->setStyleSheet(style);
    ui->Page7Label1->setStyleSheet(style);
    ui->Page8Label1->setStyleSheet(style);
    ui->Page9Label1->setStyleSheet(style);
    ui->PageALabel1->setStyleSheet(style);
    ui->PageBLabel->setStyleSheet(style);
    ui->PageCLabel->setStyleSheet(style);
    style = "QLabel {"
            "   color: rgb(102, 102, 102);"
            "}";
    ui->Page6Label2->setStyleSheet(style);
    ui->Page8Label2->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(48, 48, 48);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->Page6PushButton1->setStyleSheet(style);
    ui->Page7PushButton1->setStyleSheet(style);
    ui->Page9PushButton1->setStyleSheet(style);
    ui->PageAPushButton1->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(1, 150, 65);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->Page6PushButton2->setStyleSheet(style);
    ui->Page7PushButton2->setStyleSheet(style);
    ui->Page9PushButton2->setStyleSheet(style);
    ui->PageAPushButton2->setStyleSheet(style);
    ui->PageBPushButton->setStyleSheet(style);
    ui->PageCPushButton->setStyleSheet(style);
    style = "QCheckBox {"
            "    color: rgb(102, 102, 102);"
            "    spacing: 6px;"
            "}"
            "QCheckBox::indicator {"
            "    width: 20px;"
            "    height: 20px;"
            "}"
            "QCheckBox::indicator:unchecked {"
            "    image: url(:/Icon/CheckBox_Uncheck.png);"
            "}"
            "QCheckBox::indicator:checked {"
            "    image: url(:/Icon/CheckBox_Checked.png);"
            "}";
    ui->Page6CheckBox->setStyleSheet(style);
    ui->Page7CheckBox->setStyleSheet(style);
    ui->Page9CheckBox->setStyleSheet(style);
    style = "QProgressBar {"
            "   background-color: rgb(51, 51, 51);"
            "   border: none;"
            "   border-radius: 2px;"
            "}"
            "QProgressBar::chunk {"
            "   background-color: rgb(3, 149, 65);"
            "   border-radius: 2px;"
            "}";
    ui->Page8ProgressBar->setStyleSheet(style);
    setLanguage("English");
}
DeviceConnectorViewS1M1::~DeviceConnectorViewS1M1()
{
    delete ui;
}


// override
void DeviceConnectorViewS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height() / 12));
    QFont font(mFont);
    ui->Page0Label1->setFont(mFont);
    ui->Page1Label1->setFont(mFont);
    ui->Page2Label1->setFont(mFont);
    ui->Page3Label1->setFont(mFont);
    ui->Page4Label1->setFont(mFont);
    ui->Page5Label1->setFont(mFont);
    ui->Page6Label1->setFont(mFont);
    font.setPointSize(font.pointSize() - 4);
    ui->Page6Label2->setFont(font);
    ui->Page7Label1->setFont(mFont);
    ui->Page8Label1->setFont(mFont);
    ui->Page8Label2->setFont(font);
    ui->Page9Label1->setFont(mFont);
    ui->PageALabel1->setFont(mFont);
    ui->PageBLabel->setFont(mFont);
    ui->PageCLabel->setFont(mFont);
    ui->Page6PushButton1->setFont(mFont);
    ui->Page6PushButton2->setFont(mFont);
    ui->Page7PushButton1->setFont(mFont);
    ui->Page7PushButton2->setFont(mFont);
    ui->Page9PushButton1->setFont(mFont);
    ui->Page9PushButton2->setFont(mFont);
    ui->PageAPushButton1->setFont(mFont);
    ui->PageAPushButton2->setFont(mFont);
    ui->PageBPushButton->setFont(mFont);
    ui->PageCPushButton->setFont(mFont);
    ui->Page6CheckBox->setFont(font);
    ui->Page7CheckBox->setFont(font);
    ui->Page9CheckBox->setFont(font);
    QString style;
    style = QString("QCheckBox {"
                    "    color: rgb(255, 255, 255);"
                    "    spacing: %1px;"
                    "}"
                    "QCheckBox::indicator {"
                    "    width: %2px;"
                    "    height: %2px;"
                    "}"
                    "QCheckBox::indicator:unchecked {"
                    "    image: url(:/Icon/CheckBox_Uncheck.png);"
                    "}"
                    "QCheckBox::indicator:checked {"
                    "    image: url(:/Icon/CheckBox_Checked.png);"
                    "}").arg(height() * 0.02).arg(height() * 0.05);
    ui->Page6CheckBox->setStyleSheet(style);
    ui->Page7CheckBox->setStyleSheet(style);
    ui->Page9CheckBox->setStyleSheet(style);
}
void DeviceConnectorViewS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void DeviceConnectorViewS1M1::setLanguage(QString language)
{
    if(language == "English")
    {
        ui->Page0Label1->setText("No device connected");
        ui->Page1Label1->setText("Device connecting......");
        ui->Page2Label1->setText("Please install or update the driver");
        ui->Page3Label1->setText("Connection failed");
        ui->Page4Label1->setText("Please remove extra devices");
        ui->Page5Label1->setText("Checking for updates......");
        ui->Page6Label1->setText("Updatable version : V0.1.0");
        ui->Page6Label2->setText("Current version : V0.0.0");
        ui->Page7Label1->setText("Network error");
        ui->Page8Label1->setText("ProgressText");
        ui->Page8Label2->setText("ProgressMessages");
        ui->Page9Label1->setText("Failed to download");
        ui->PageALabel1->setText("Failed to download");
        ui->PageBLabel->setText("DFU failed. Please restart the device and try again.");
        ui->PageCLabel->setText("DFU successful. Please restart the device.");
        ui->Page6PushButton1->setText("Cancel");
        ui->Page6PushButton2->setText("Update");
        ui->Page7PushButton1->setText("Cancel");
        ui->Page7PushButton2->setText("Retry");
        ui->Page9PushButton1->setText("Cancel");
        ui->Page9PushButton2->setText("Retry");
        ui->PageAPushButton1->setText("Cancel");
        ui->PageAPushButton2->setText("Retry");
        ui->PageBPushButton->setText("Ok");
        ui->PageCPushButton->setText("Ok");
        ui->Page6CheckBox->setText("Don't check for updates");
        ui->Page7CheckBox->setText("Don't check for updates");
        ui->Page9CheckBox->setText("Don't check for updates");
    }
    else if(language == "Chinese")
    {
        ui->Page0Label1->setText("无设备连接");
        ui->Page1Label1->setText("设备连接中......");
        ui->Page2Label1->setText("请安装或更新驱动程序");
        ui->Page3Label1->setText("连接失败");
        ui->Page4Label1->setText("请移除多余设备");
        ui->Page5Label1->setText("检查更新中......");
        ui->Page6Label1->setText("最新版本 : V0.1.0");
        ui->Page6Label2->setText("当前版本 : V0.0.0");
        ui->Page7Label1->setText("网络错误");
        ui->Page8Label1->setText("进度文本");
        ui->Page8Label2->setText("进度信息");
        ui->Page9Label1->setText("下载失败");
        ui->PageALabel1->setText("下载失败");
        ui->PageBLabel->setText("固件升级失败，请重启设备重试升级");
        ui->PageCLabel->setText("固件升级成功，请重启设备");
        ui->Page6PushButton1->setText("取消");
        ui->Page6PushButton2->setText("更新");
        ui->Page7PushButton1->setText("取消");
        ui->Page7PushButton2->setText("重试");
        ui->Page9PushButton1->setText("取消");
        ui->Page9PushButton2->setText("重试");
        ui->PageAPushButton1->setText("取消");
        ui->PageAPushButton2->setText("重试");
        ui->PageBPushButton->setText("确定");
        ui->PageCPushButton->setText("确定");
        ui->Page6CheckBox->setText("不再检查更新");
        ui->Page7CheckBox->setText("不再检查更新");
        ui->Page9CheckBox->setText("不再检查更新");
    }
    mLanguage = language;
}
void DeviceConnectorViewS1M1::modifyVersion(QString current, QString updatable)
{
    if(mLanguage == "English")
    {
        ui->Page6Label1->setText("Updatable version : V" + updatable + (updatable.section('.', -1) == "0" ? "" : " Beta"));
        ui->Page6Label2->setText("Current version : V" + current + (current.section('.', -1) == "0" ? "" : " Beta"));
    }
    else if(mLanguage == "Chinese")
    {
        ui->Page6Label1->setText("最新版本 : V" + updatable + (updatable.section('.', -1) == "0" ? "" : " Beta"));
        ui->Page6Label2->setText("当前版本 : V" + current + (current.section('.', -1) == "0" ? "" : " Beta"));
    }
}
void DeviceConnectorViewS1M1::modifyUpdateMessage(QString text)
{
    ui->Page8Label1->setText(text);
}
void DeviceConnectorViewS1M1::modifyUpdateProgress(QString text)
{
    ui->Page8Label2->setText(text);
}
void DeviceConnectorViewS1M1::modifyUpdateProgress(int progress)
{
    ui->Page8ProgressBar->setValue(progress);
}
int DeviceConnectorViewS1M1::currentPage()
{
    return ui->stackedWidget->currentIndex();
}
void DeviceConnectorViewS1M1::showPage(int page)
{
    ui->stackedWidget->setCurrentIndex(page);
}


// slot
void DeviceConnectorViewS1M1::on_Page6PushButton1_clicked()
{
    emit attributeChanged("Page6", "Update", "Cancel");
}
void DeviceConnectorViewS1M1::on_Page6PushButton2_clicked()
{
    emit attributeChanged("Page6", "Update", "UpdateSoftware");
}
void DeviceConnectorViewS1M1::on_Page6CheckBox_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Checked)
    {
        emit attributeChanged("Page6", "Update", "AutoCheckOFF");
    }
    else
    {
        emit attributeChanged("Page6", "Update", "AutoCheckON");
    }
}
void DeviceConnectorViewS1M1::on_Page7PushButton1_clicked()
{
    emit attributeChanged("Page7", "Update", "Cancel");
}
void DeviceConnectorViewS1M1::on_Page7PushButton2_clicked()
{
    emit attributeChanged("Page7", "Update", "RetryFetch");
}
void DeviceConnectorViewS1M1::on_Page7CheckBox_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Checked)
    {
        emit attributeChanged("Page7", "Update", "AutoCheckOFF");
    }
    else
    {
        emit attributeChanged("Page7", "Update", "AutoCheckON");
    }
}
void DeviceConnectorViewS1M1::on_Page9PushButton1_clicked()
{
    emit attributeChanged("Page9", "Update", "Cancel");
}
void DeviceConnectorViewS1M1::on_Page9PushButton2_clicked()
{
    emit attributeChanged("Page9", "Update", "RetryDownloadSoftware");
}
void DeviceConnectorViewS1M1::on_Page9CheckBox_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Checked)
    {
        emit attributeChanged("Page9", "Update", "AutoCheckOFF");
    }
    else
    {
        emit attributeChanged("Page9", "Update", "AutoCheckON");
    }
}
void DeviceConnectorViewS1M1::on_PageAPushButton1_clicked()
{
    emit attributeChanged("PageA", "Update", "Cancel");
}
void DeviceConnectorViewS1M1::on_PageAPushButton2_clicked()
{
    emit attributeChanged("PageA", "Update", "RetryDownloadFirmware");
}
void DeviceConnectorViewS1M1::on_PageBPushButton_clicked()
{
    emit attributeChanged("PageB", "Update", "Cancel");
}
void DeviceConnectorViewS1M1::on_PageCPushButton_clicked()
{
    emit attributeChanged("PageC", "Update", "Cancel");
}

