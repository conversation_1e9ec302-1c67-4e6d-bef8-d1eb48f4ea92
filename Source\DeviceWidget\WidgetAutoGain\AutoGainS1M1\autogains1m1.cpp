#include "autogains1m1.h"
#include <QPainter>
#include <QStackedWidget>
#include <QPushButton>
#include <QLabel>
#include <QStackedWidget>
#include <qfont.h>
#include <QTimer>
#include "circles1m1.h"
#include "framelesswindow.h"
#include "globalfont.h"

AutoGainS1M1_WidgetBase::AutoGainS1M1_WidgetBase(QWidget* parent)
    : QWidget(parent)
{
    
}

void AutoGainS1M1_WidgetBase::resizeEvent(QResizeEvent* e)
{
    QWidget::resizeEvent(e);
    updateControlsGeometry();
}

AutoGainS1M1_WidgetS1M1::AutoGainS1M1_WidgetS1M1(QWidget* parent)
    : AutoGainS1M1_WidgetBase(parent)
{    
    mCircleWidget = new CircleS1M1(this);
    mCircleWidget->setStatus((CircleS1M1::Status)AutoGainS1M1::Start);
    mStartButton = new QPushButton(this);
    mMainLabel = new QLabel(this);
    mSubLabel = new QLabel(this);

    mMainLabel->setAlignment(Qt::AlignCenter);
    mSubLabel->setAlignment(Qt::AlignCenter);
    mMainLabel->setStyleSheet("background:transparent;color: rgba(255, 255, 255, 1)");
    mSubLabel->setStyleSheet("background:transparent;color: rgba(255, 255, 255, 1)");

    connect(mStartButton, &QPushButton::clicked, this, [=](){
        emit attributeChanged(this->objectName(), "Start", "Start");
    });
}

void AutoGainS1M1_WidgetS1M1::updateControlsGeometry()
{
    QRect rect = this->rect();
    
    struct Layout {
        double verticalSpacing = 0.04;
        double verticalSpacing2 = 0.02;
        double verticalSpacing3 = 0.06;

        struct Circle {
            double heightRatio = 0.4;
            double topRatio = 0;
        } circle;
        
        struct MainLabel {
            double widthRatio = 0.8;
            double heightRatio = 0.2;
            double topRatio;
        } mainLabel;
        
        struct SubLabel {
            double widthRatio = 0.8;
            double heightRatio = 0.08;
            double topRatio;
        } subLabel;
        
        struct Button {
            double widthRatio = 0.35;
            double heightRatio = 0.12;
            double topRatio;
        } button;
    } layout;

    layout.mainLabel.topRatio = layout.circle.topRatio + layout.circle.heightRatio + layout.verticalSpacing;
    layout.subLabel.topRatio = layout.mainLabel.topRatio + layout.mainLabel.heightRatio + layout.verticalSpacing2;
    layout.button.topRatio = layout.subLabel.topRatio + layout.subLabel.heightRatio + layout.verticalSpacing3;

    const int circleSize = qMin((double)rect.width(), rect.height() * layout.circle.heightRatio);
    mCircleWidget->setGeometry(
        (rect.width() - circleSize) / 2,
        rect.height() * layout.circle.topRatio,
        circleSize,
        circleSize
    );
    
    const int mainLabelWidth = rect.width() * layout.mainLabel.widthRatio;
    const int mainLabelHeight = rect.height() * layout.mainLabel.heightRatio;
    mMainLabel->setGeometry(
        (rect.width() - mainLabelWidth) / 2,
        rect.height() * layout.mainLabel.topRatio,
        mainLabelWidth,
        mainLabelHeight
    );
    
    const int subLabelWidth = rect.width() * layout.subLabel.widthRatio;
    const int subLabelHeight = rect.height() * layout.subLabel.heightRatio;
    mSubLabel->setGeometry(
        (rect.width() - subLabelWidth) / 2,
        rect.height() * layout.subLabel.topRatio,
        subLabelWidth,
        subLabelHeight
    );
    
    const int buttonWidth = rect.width() * layout.button.widthRatio;
    const int buttonHeight = rect.height() * layout.button.heightRatio;
    mStartButton->setGeometry(
        (rect.width() - buttonWidth) / 2,
        rect.height() * layout.button.topRatio,
        buttonWidth,
        buttonHeight
    );

    auto setFont = [](QWidget* widget, int row, double fontRatio){
        QFont font = widget->font();
        auto fontSize = GLBFHandle.getSuitablePixelSize(font, widget->height()/row);
        font.setPixelSize(fontSize*fontRatio);
        widget->setFont(font);
    };
    setFont(mMainLabel, 2, 0.85);
    setFont(mSubLabel, 1, 0.9);
    setFont(mStartButton, 1, 0.8);

    mStartButton->setStyleSheet(QString("color: rgba(255, 255, 255, 1); background: rgba(1, 150, 65, 1);border-radius:%1px").arg(mStartButton->height() * 0.2));
}

void AutoGainS1M1_WidgetS1M1::setCircleText(const QString& text)
{
    mCircleWidget->setValue(text.toInt());
}

void AutoGainS1M1_WidgetS1M1::setMainText(const QString& text)
{
    mMainLabel->setText(text);
}

void AutoGainS1M1_WidgetS1M1::setSubText(const QString& text)
{
    mSubLabel->setText(text);
}

void AutoGainS1M1_WidgetS1M1::setStartButtonText(const QString& text)
{
    mStartButton->setText(text);
}

AutoGainS1M1_WidgetS1M2::AutoGainS1M1_WidgetS1M2(QWidget* parent)
    : AutoGainS1M1_WidgetBase(parent)
{    
    mCircleWidget = new CircleS1M1(this);
    mCircleWidget->setStatus((CircleS1M1::Status)AutoGainS1M1::Detect);
    mMainLabel = new QLabel(this);
    mCancelButton = new QPushButton(this);

    mMainLabel->setAlignment(Qt::AlignCenter);
    mMainLabel->setStyleSheet("background:transparent;color: rgba(255, 255, 255, 1)");
    
    mCancelButton->setMinimumHeight(20);
    connect(mCancelButton, &QPushButton::clicked, this, [=](){
        emit attributeChanged(this->objectName(), "Cancel", "Cancel");
    });
}

void AutoGainS1M1_WidgetS1M2::updateControlsGeometry()
{
    QRect rect = this->rect();
        
    struct Layout {
        double verticalSpacing = 0.1;
        double verticalSpacing2 = 0.1;

        struct Circle {
            double heightRatio = 0.4;
            double topRatio = 0;
        } circle;
        
        struct MainLabel {
            double widthRatio = 0.8;
            double heightRatio = 0.2;
            double topRatio;
        } mainLabel;
        
        struct Button {
            double widthRatio = 0.35;
            double heightRatio = 0.12;
            double topRatio;
        } button;
    } layout;

    layout.mainLabel.topRatio = layout.circle.topRatio + layout.circle.heightRatio + layout.verticalSpacing;
    layout.button.topRatio = layout.mainLabel.topRatio+ layout.mainLabel.heightRatio  + layout.verticalSpacing2;

    const int circleSize = qMin((double)rect.width(), rect.height() * layout.circle.heightRatio);
    mCircleWidget->setGeometry(
        (rect.width() - circleSize) / 2,
        rect.height() * layout.circle.topRatio,
        circleSize,
        circleSize
    );
    
    const int mainLabelWidth = rect.width() * layout.mainLabel.widthRatio;
    const int mainLabelHeight = rect.height() * layout.mainLabel.heightRatio;
    mMainLabel->setGeometry(
        (rect.width() - mainLabelWidth) / 2,
        rect.height() * layout.mainLabel.topRatio,
        mainLabelWidth,
        mainLabelHeight
    );
    
    const int buttonWidth = rect.width() * layout.button.widthRatio;
    const int buttonHeight = rect.height() * layout.button.heightRatio;
    mCancelButton->setGeometry(
        (rect.width() - buttonWidth) / 2,
        rect.height() * layout.button.topRatio,
        buttonWidth,
        buttonHeight
    );

    auto setFont = [](QWidget* widget, int row, double fontRatio){
        QFont font = widget->font();
        auto fontSize = GLBFHandle.getSuitablePixelSize(font, widget->height()/row);
        font.setPixelSize(fontSize*fontRatio);
        widget->setFont(font);
    };
    setFont(mMainLabel, 2, 0.85);
    setFont(mCancelButton, 1, 0.8);

    mCancelButton->setStyleSheet(QString("color: rgba(255, 255, 255, 1); background: rgba(48, 48, 48, 1);border-radius:%1px").arg(mCancelButton->height() * 0.2));
}

void AutoGainS1M1_WidgetS1M2::setCircleText(const QString& text)
{
    mCircleWidget->setValue(text.toInt());
}

void AutoGainS1M1_WidgetS1M2::setMainText(const QString& text)
{
    mMainLabel->setText(text);
}

void AutoGainS1M1_WidgetS1M2::setCancelButtonText(const QString& text)
{
    mCancelButton->setText(text);
}

AutoGainS1M1_WidgetS1M3::AutoGainS1M1_WidgetS1M3(QWidget* parent)
    : AutoGainS1M1_WidgetBase(parent)
{    
    mCircleWidget = new CircleS1M1(this);
    mCircleWidget->setStatus((CircleS1M1::Status)AutoGainS1M1::Complete);
    mMainLabel = new QLabel(this);
    mApplyButton = new QPushButton(this);
    mAbandonButton = new QPushButton(this);

    mMainLabel->setAlignment(Qt::AlignCenter);
    mMainLabel->setStyleSheet("background:transparent;color: rgba(255, 255, 255, 1)");
    
    mApplyButton->setMinimumHeight(20);
    mAbandonButton->setMinimumHeight(20);
    connect(mApplyButton, &QPushButton::clicked, this, [=](){
        emit attributeChanged(this->objectName(), "Apply", "Apply");
    });
    connect(mAbandonButton, &QPushButton::clicked, this, [=](){
        emit attributeChanged(this->objectName(), "Abandon", "Abandon");
    });
}

void AutoGainS1M1_WidgetS1M3::updateControlsGeometry()
{
    QRect rect = this->rect();

    struct Layout {
        double verticalSpacing = 0.1;
        double verticalSpacing2 = 0.1;
        double hSpacing = 0.13;
        double hSpacing2 = 0.04;

        struct Circle {
            double heightRatio = 0.4;
            double topRatio = 0;
        } circle;
        
        struct MainLabel {
            double widthRatio = 0.8;
            double heightRatio = 0.2;
            double topRatio;
        } mainLabel;
        
        struct Button {
            double widthRatio = 0.35;
            double heightRatio = 0.12;
            double topRatio;
            double applyLeftRatio;
            double abandonLeftRatio;
        } button;
    } layout;

    layout.mainLabel.topRatio = layout.circle.topRatio + layout.circle.heightRatio + layout.verticalSpacing;
    layout.button.topRatio = layout.mainLabel.topRatio+ layout.mainLabel.heightRatio  + layout.verticalSpacing2;
    layout.button.abandonLeftRatio = layout.hSpacing;
    layout.button.applyLeftRatio = layout.hSpacing + layout.button.widthRatio + layout.hSpacing2;

    const int circleSize = qMin((double)rect.width(), rect.height() * layout.circle.heightRatio);
    mCircleWidget->setGeometry(
        (rect.width() - circleSize) / 2,
        rect.height() * layout.circle.topRatio,
        circleSize,
        circleSize
    );
    
    const int mainLabelWidth = rect.width() * layout.mainLabel.widthRatio;
    const int mainLabelHeight = rect.height() * layout.mainLabel.heightRatio;
    mMainLabel->setGeometry(
        (rect.width() - mainLabelWidth) / 2,
        rect.height() * layout.mainLabel.topRatio,
        mainLabelWidth,
        mainLabelHeight
    );
    
    const int buttonWidth = rect.width() * layout.button.widthRatio;
    const int buttonHeight = rect.height() * layout.button.heightRatio;
    
    mAbandonButton->setGeometry(
        rect.width() * layout.button.abandonLeftRatio,
        rect.height() * layout.button.topRatio,
        buttonWidth,
        buttonHeight
    );

    mApplyButton->setGeometry(
        rect.width() * layout.button.applyLeftRatio,
        rect.height() * layout.button.topRatio,
        buttonWidth,
        buttonHeight
    );

    auto setFont = [](QWidget* widget, int row, double fontRatio){
        QFont font = widget->font();
        auto fontSize = GLBFHandle.getSuitablePixelSize(font, widget->height()/row);
        font.setPixelSize(fontSize*fontRatio);
        widget->setFont(font);
    };
    setFont(mMainLabel, 2, 0.85);
    setFont(mApplyButton, 1, 0.8);
    setFont(mAbandonButton, 1, 0.8);

    mAbandonButton->setStyleSheet(QString("color: rgba(255, 255, 255, 1); background: rgba(48, 48, 48, 1);border-radius:%1px").arg(mAbandonButton->height() * 0.2));
    mApplyButton->setStyleSheet(QString("color: rgba(255, 255, 255, 1); background: rgba(1, 150, 65, 1);border-radius:%1px").arg(mApplyButton->height() * 0.2));
}

void AutoGainS1M1_WidgetS1M3::setCircleText(const QString& text)
{
    mCircleWidget->setValue(text.toInt());
}

void AutoGainS1M1_WidgetS1M3::setMainText(const QString& text)
{
    mMainLabel->setText(text);
}

void AutoGainS1M1_WidgetS1M3::setApplyButtonText(const QString& text)
{
    mApplyButton->setText(text);
}

void AutoGainS1M1_WidgetS1M3::setAbandonButtonText(const QString& text)
{
    mAbandonButton->setText(text);
}

AutoGainS1M1_WidgetS1M4::AutoGainS1M1_WidgetS1M4(QWidget* parent)
    : AutoGainS1M1_WidgetBase(parent)
{    
    mCircleWidget = new CircleS1M1(this);
    mCircleWidget->setStatus((CircleS1M1::Status)AutoGainS1M1::Error);
    mMainLabel = new QLabel(this);
    mRetryButton = new QPushButton(this);

    mMainLabel->setAlignment(Qt::AlignCenter);
    mMainLabel->setStyleSheet("background:transparent;color: rgba(255, 255, 255, 1)");
    
    mRetryButton->setMinimumHeight(20);
    connect(mRetryButton, &QPushButton::clicked, this, [=](){
        emit attributeChanged(this->objectName(), "Retry", "Retry");
    });
}

void AutoGainS1M1_WidgetS1M4::updateControlsGeometry()
{
    QRect rect = this->rect();
        
    struct Layout {
        double verticalSpacing = 0.05;
        double verticalSpacing2 = 0.05;

        struct Circle {
            double heightRatio = 0.4;
            double topRatio = 0;
        } circle;
        
        struct MainLabel {
            double widthRatio = 0.8;
            double heightRatio = 0.3;
            double topRatio;
        } mainLabel;
        
        struct Button {
            double widthRatio = 0.35;
            double heightRatio = 0.12;
            double topRatio;
        } button;
    } layout;

    layout.mainLabel.topRatio = layout.circle.topRatio + layout.circle.heightRatio + layout.verticalSpacing;
    layout.button.topRatio = layout.mainLabel.topRatio+ layout.mainLabel.heightRatio  + layout.verticalSpacing2;

    const int circleSize = qMin((double)rect.width(), rect.height() * layout.circle.heightRatio);
    mCircleWidget->setGeometry(
        (rect.width() - circleSize) / 2,
        rect.height() * layout.circle.topRatio,
        circleSize,
        circleSize
    );
    
    const int mainLabelWidth = rect.width() * layout.mainLabel.widthRatio;
    const int mainLabelHeight = rect.height() * layout.mainLabel.heightRatio;
    mMainLabel->setGeometry(
        (rect.width() - mainLabelWidth) / 2,
        rect.height() * layout.mainLabel.topRatio,
        mainLabelWidth,
        mainLabelHeight
    );
    
    const int buttonWidth = rect.width() * layout.button.widthRatio;
    const int buttonHeight = rect.height() * layout.button.heightRatio;
    mRetryButton->setGeometry(
        (rect.width() - buttonWidth) / 2,
        rect.height() * layout.button.topRatio,
        buttonWidth,
        buttonHeight
    );

    auto setFont = [](QWidget* widget, int row, double fontRatio){
        QFont font = widget->font();
        auto fontSize = GLBFHandle.getSuitablePixelSize(font, widget->height()/row);
        font.setPixelSize(fontSize*fontRatio);
        widget->setFont(font);
    };
    setFont(mMainLabel, 3, 0.85);
    setFont(mRetryButton, 1, 0.8);

    mRetryButton->setStyleSheet(QString("color: rgba(255, 255, 255, 1); background: rgba(48, 48, 48, 1);border-radius:%1px").arg(mRetryButton->height() * 0.2));
}

void AutoGainS1M1_WidgetS1M4::setCircleText(const QString& text)
{
    mCircleWidget->setValue(text.toInt());
}

void AutoGainS1M1_WidgetS1M4::setMainText(const QString& text)
{
    mMainLabel->setText(text);
}

void AutoGainS1M1_WidgetS1M4::setRetryButtonText(const QString& text)
{
    mRetryButton->setText(text);
}


AutoGainS1M1::AutoGainS1M1(FramelessWindow* parent, const QString& name)
    : FramelessWindow(parent), AppSettingsObserver(name)
    , mStackedWidget(new QStackedWidget(this))
    , mCurrentState(invalid)
    , mTimer(new QTimer(this))
    , mTimeoutSeconds(15)
    , mHasCalculated(false)
    ,mMinDb(-70)
    ,mMaxDb(-6)
{
    AppSettingsSubject::instance().addObserver(this);
    setRightBottomDraggable();
    setMinimumSize(150, 150);
    setCloseButtonReturnCode(-2147483648);
    resize(260, 260);
    QHash<State, std::function<AutoGainS1M1_WidgetBase*(QWidget*)>> stateWidgetCreators = {
        {Start, [](QWidget* parent) { return new AutoGainS1M1_WidgetS1M1(parent); }},
        {Detect, [](QWidget* parent) { return new AutoGainS1M1_WidgetS1M2(parent); }},
        {Complete, [](QWidget* parent) { return new AutoGainS1M1_WidgetS1M3(parent); }},
        {Error, [](QWidget* parent) { return new AutoGainS1M1_WidgetS1M4(parent); }}
    };

    for (auto it = stateWidgetCreators.constBegin(); it != stateWidgetCreators.constEnd(); ++it) {
        AutoGainS1M1_WidgetBase* widget = it.value()(this);
        mHashStateToWidget.insert(it.key(), widget);
        mStackedWidget->addWidget(widget);
        connect(widget, &AutoGainS1M1_WidgetBase::attributeChanged, this, &AutoGainS1M1::handleAttributeChanged);
    }
    setCentralWidget(mStackedWidget);

    connect(mTimer, &QTimer::timeout, this, &AutoGainS1M1::onTimerTimeout);

    switchToState(Start);

    changeLanguage("English");
}

AutoGainS1M1::~AutoGainS1M1() {

}

void AutoGainS1M1::setName(const QString& name) {
    setObjectName(name);
    setObserverName(name);
}

template<typename T>
T* AutoGainS1M1::getWidget(State state) {
    return qobject_cast<T*>(mHashStateToWidget.value(state));
}

void AutoGainS1M1::setDbRange(float minDb, float maxDb) {
    mMinDb = minDb;
    mMaxDb = maxDb;
}
void AutoGainS1M1::sampleLevel(float level) {
    if (mTimer->isActive()) {
        mSampleValues.insert(level);
    }
}

float AutoGainS1M1::getFinalSampleLevel() {
    if (mHasCalculated) {
        return mFinalGainValue;
    }
    
    if (mSampleValues.size() < 10) {
        return 0;
    }

    float sum = 0;
    auto it = mSampleValues.begin();
    for (int i = 0; i < 10 && it != mSampleValues.end(); ++i, ++it) {
        if(i >= 5){
            sum += *it;
        }
    }

    mFinalGainValue = sum / 5;
    mHasCalculated = true;
    return mFinalGainValue;
}

void AutoGainS1M1::setFont(const QFont& font) {
    FramelessWindow::setFont(font);
    setAllChildFont(this, font);
}

void AutoGainS1M1::switchToState(State state) {
    if(mCurrentState != state && mHashStateToWidget.contains(state)) {
        mCurrentState = state;
        mStackedWidget->setCurrentWidget(mHashStateToWidget.value(state));
    }
}

int AutoGainS1M1::exec() {
    switchToState(Start);
    return FramelessWindow::exec();
}

void AutoGainS1M1::handleAttributeChanged(QString objectName, QString attribute, QString value) {
    if(attribute.startsWith("Start")) {
        if (value != "0") {
            emit attributeChanged("Start", "1");
        }
        switchToState(Detect);
        startTimer();
    } else if(attribute.startsWith("Cancel")) {
        stopTimer();
        done(-2147483648);
    } else if(attribute.startsWith("Apply")) {
        done(getFinalSampleLevel()*10);
    } else if(attribute.startsWith("Abandon")) {
        done(-2147483648);
    } else if(attribute.startsWith("Retry")) {
        handleAttributeChanged(objectName, "Start", "0");
    }
}

void AutoGainS1M1::setAllChildFont(QWidget* widget, const QFont& font)
{
    for (auto child : widget->children()) {
        QWidget *widget = qobject_cast<QWidget *>(child);
        if (widget) {
            widget->setFont(font);
            setAllChildFont(widget, font);
        }
    }
}

void AutoGainS1M1::setStartText(const QString& circleText, const QString& mainText, const QString& subText, const QString& buttonText) {
    if (auto widget = getWidget<AutoGainS1M1_WidgetS1M1>(Start)) {
        widget->setCircleText(circleText);
        widget->setMainText(mainText);
        widget->setSubText(subText);
        widget->setStartButtonText(buttonText);
    }
}

void AutoGainS1M1::setDetectText(const QString& circleText, const QString& mainText, const QString& cancelText) {
    if (auto widget = getWidget<AutoGainS1M1_WidgetS1M2>(Detect)) {
        widget->setCircleText(circleText);
        widget->setMainText(mainText);
        widget->setCancelButtonText(cancelText);
    }
}

void AutoGainS1M1::setCompleteText(const QString& circleText, const QString& mainText, const QString& applyText, const QString& abandonText) {
    if (auto widget = getWidget<AutoGainS1M1_WidgetS1M3>(Complete)) {
        widget->setCircleText(circleText);
        widget->setMainText(mainText);
        widget->setApplyButtonText(applyText);
        widget->setAbandonButtonText(abandonText);
    }
}

void AutoGainS1M1::setErrorText(const QString& circleText, const QString& mainText, const QString& retryText) {
    if (auto widget = getWidget<AutoGainS1M1_WidgetS1M4>(Error)) {
        widget->setCircleText(circleText);
        widget->setMainText(mainText);
        widget->setRetryButtonText(retryText);
    }
}

void AutoGainS1M1::startTimer()
{
    mTimeoutSeconds = 15;
    mSampleValues.clear();
    mHasCalculated = false;
    if (auto widget = getWidget<AutoGainS1M1_WidgetS1M2>(Detect)) {
        widget->setCircleText(QString::number(mTimeoutSeconds));
    }
    mTimer->start(1000);
}

void AutoGainS1M1::stopTimer()
{
    mTimer->stop();
}

void AutoGainS1M1::changeLanguage(QString language)
{
    if (language == "Chinese") {
        setStartText("", "点击\"开始\"进行自动增益匹配", "(输入增益将重置为0)", "开始");
        setDetectText(QString::number(mTimeoutSeconds),"正在检测输入电平...\n请输入信号","取消");
        setCompleteText("","自动增益匹配已完成","应用","返回");
        setErrorText("","自动增益检测失败,\n请检查设备连接并重试","重试");
    } else if( language == "English") {
        setStartText("", "Click Start\nto begin autogain match", "(input gain will reset to 0)", "Start");
        setDetectText(QString::number(mTimeoutSeconds),"Detecting input level...\nPlease input signal","Cancel");
        setCompleteText("","Autogain match\nhas been completed","Apply","Back");
        setErrorText("","Autogain detection failed.\nPlease check your device\nconnection and try again","Retry");
    }
}

void AutoGainS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        changeLanguage(value);
    }
}

void AutoGainS1M1::onTimerTimeout()
{
    mTimeoutSeconds--;
    if (auto widget = getWidget<AutoGainS1M1_WidgetS1M2>(Detect)) {
        widget->setCircleText(QString::number(mTimeoutSeconds));
    }
    if (mTimeoutSeconds <= 0) {
        stopTimer();
        const float epsilon = 1e-6f;
        State state = Error;
        if(auto level = getFinalSampleLevel(); level >= mMinDb - epsilon && level <= mMaxDb + epsilon) {
            state = Complete;
        }
        QTimer::singleShot(50, this, [this, state](){
            switchToState(state);
        });
        return;
    }
}
