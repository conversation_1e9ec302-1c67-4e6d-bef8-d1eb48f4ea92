#ifndef EQWIDGETIEM_H
#define EQWIDGETIEM_H

#include <QFrame>
#include "eqwidgetitemdata.h"

class QLabel;
class ComboBoxS1M3;
class DialS1M5;
class QCheckBox;
class EqWidgetIem : public QFrame
{
    Q_OBJECT

public:
    explicit EqWidgetIem(int index, QWidget* parent = nullptr);
    ~EqWidgetIem();

    EqWidgetIem& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setData(const QString& type, const QString& data);
    void setData(const EqWidgetItemData& data);
    EqWidgetItemData getData() const;
    void setLanguage(QString language);

    void setIndex(int index);
    int getIndex() const { return mIndex; }

protected:
    void resizeEvent(QResizeEvent* event) override;
    void adjustFontAndSize();

private:
    void setupUI();
    void setupConnections();

    int mIndex;
    EqWidgetItemData mData;

    QFont mFont;
    double mSizeFactor;

    QLabel* mItemLabel;

    ComboBoxS1M3* mTypeComboBox;

    DialS1M5* mGainDial;
    DialS1M5* mFrequencyDial;
    DialS1M5* mQValueDial;

    QCheckBox* mEnabledCheckBox;

    QHash<QObject*,QHash<QString,QVector<QString>>> mHashLanguages;

signals:
    void dataChanged(int index, const EqWidgetItemData& data);
};

#endif // EQWIDGETIEM_H
